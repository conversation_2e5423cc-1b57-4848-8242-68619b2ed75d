# Custom OpenAI-Compatible Embedding Provider Guide

本指南介绍如何使用扩展后的OpenAI embedding provider来接入自定义的OpenAI兼容接口，包括阿里云等第三方embedding服务。

## 功能特性

### 新增功能
- ✅ 支持自定义base_url配置
- ✅ 支持自定义API密钥配置
- ✅ 支持任意模型名称（不限制于预定义的OpenAI模型）
- ✅ 灵活的维度配置
- ✅ 保持与原有OpenAI模型的完全兼容性

### 向后兼容性
- ✅ 原有的OpenAI配置方式完全不受影响
- ✅ 环境变量OPENAI_API_KEY仍然有效
- ✅ 所有现有的OpenAI模型配置继续工作

## 配置方式

### 1. 使用配置文件

在你的`r2r.toml`配置文件中添加以下配置：

```toml
[embedding]
provider = "openai"
base_model = "aliyun/text-embedding-v4"
base_dimension = 1024
base_url = "http://172.17.9.46:3000/v1"
api_key = "sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI"
batch_size = 128
concurrent_request_limit = 256
```

### 2. 使用环境变量

你也可以通过环境变量设置API密钥：

```bash
export OPENAI_API_KEY="sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI"
```

然后在配置文件中：

```toml
[embedding]
provider = "openai"
base_model = "aliyun/text-embedding-v4"
base_dimension = 1024
base_url = "http://172.17.9.46:3000/v1"
# api_key 可以省略，将使用环境变量
```

## 配置参数说明

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `provider` | string | ✅ | 必须设置为 "openai" |
| `base_model` | string | ✅ | 模型名称，支持任意名称 |
| `base_dimension` | int | ✅ | embedding向量维度 |
| `base_url` | string | ❌ | 自定义API端点URL |
| `api_key` | string | ❌ | 自定义API密钥（优先级高于环境变量） |

## 使用示例

### 示例1：阿里云text-embedding-v4模型

```toml
[embedding]
provider = "openai"
base_model = "aliyun/text-embedding-v4"
base_dimension = 1024
base_url = "http://172.17.9.46:3000/v1"
api_key = "sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI"
```

### 示例2：其他OpenAI兼容服务

```toml
[embedding]
provider = "openai"
base_model = "custom/my-embedding-model"
base_dimension = 768
base_url = "https://api.example.com/v1"
api_key = "your-custom-api-key"
```

### 示例3：标准OpenAI配置（保持不变）

```toml
[embedding]
provider = "openai"
base_model = "text-embedding-3-small"
base_dimension = 512
# 不需要base_url，使用官方OpenAI API
```

## 技术实现细节

### 模型检测机制
- 系统会自动检测是否为已知的OpenAI模型
- 对于自定义模型，会使用灵活的配置策略
- 自定义模型使用`cl100k_base`作为默认tokenizer

### 维度处理
- 对于已知OpenAI模型：验证维度是否支持
- 对于自定义模型：使用配置的维度，如未指定则默认为1536

### 错误处理
- 提供详细的错误信息和配置建议
- 自动回退到合理的默认值

## 测试验证

使用提供的测试脚本验证配置：

```bash
python test_custom_embedding.py
```

测试脚本会验证：
- 自定义endpoint的连接
- 单个文本的embedding生成
- 批量文本的embedding生成
- tokenization功能
- 与标准OpenAI配置的兼容性

## 故障排除

### 常见问题

1. **连接失败**
   - 检查`base_url`是否正确
   - 确认网络连接和防火墙设置

2. **认证失败**
   - 验证`api_key`是否正确
   - 检查API密钥是否有效且有足够权限

3. **维度不匹配**
   - 确认`base_dimension`与模型实际输出维度一致
   - 查看模型文档获取正确的维度信息

4. **模型不支持**
   - 确认endpoint支持指定的模型名称
   - 检查模型名称格式是否正确

### 日志信息

系统会记录以下信息：
- 使用的endpoint类型（官方/自定义）
- 模型类型检测结果
- 维度配置警告
- tokenizer选择信息

## 最佳实践

1. **安全性**
   - 不要在配置文件中硬编码API密钥
   - 优先使用环境变量管理敏感信息

2. **性能优化**
   - 根据实际需求调整`batch_size`
   - 合理设置`concurrent_request_limit`

3. **监控**
   - 监控embedding生成的延迟和成功率
   - 定期验证模型输出质量

4. **测试**
   - 在生产环境部署前充分测试
   - 验证embedding维度和质量符合预期
