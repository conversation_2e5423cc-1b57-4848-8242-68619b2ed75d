# Environment variables for LLM provider(s)
# OpenAI API配置 - 用于自定义OpenAI兼容端点
export OPENAI_API_KEY=sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI
export OPENAI_BASE_URL=http://***********:3000/v1

# 其他LLM提供者的API密钥（根据需要取消注释）
# export ANTHROPIC_API_KEY=...
# export VERTEX_API_KEY=...
# export XAI_API_KEY=...
# Add other provider keys as needed

# Environment variables for the Postgres database
export R2R_POSTGRES_USER=postgres
export R2R_POSTGRES_PASSWORD=postgres
export R2R_POSTGRES_HOST=************
export R2R_POSTGRES_PORT=5432
export R2R_POSTGRES_DBNAME=postgres
export R2R_PROJECT_NAME=cscs_rag
