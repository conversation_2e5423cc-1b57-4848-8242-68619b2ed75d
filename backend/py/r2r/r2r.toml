# R2R (RAG to Riches) 配置文件
# 这是一个用于配置RAG（检索增强生成）系统的配置文件

[app]
# 应用程序全局设置，可通过 `r2r_config.agent.app` 访问
# project_name = "r2r_default" # 可选项，也可以通过环境变量 `R2R_PROJECT_NAME` 设置项目名称

# 每个用户的默认最大文档数量限制
default_max_documents_per_user = 10_000

# 每个用户的默认最大文档块数量限制（用于向量检索）
default_max_chunks_per_user = 10_000_000

# 每个用户的默认最大集合数量限制
default_max_collections_per_user = 5_000

# 设置默认最大上传文件大小为200GB（用于本地测试）
# 214748364800 字节 = 200GB
default_max_upload_size = 214748364800

# 用于内部操作的快速LLM模型，如生成对话名称等轻量级任务
fast_llm = "openai/volcengine/deepseek-v3"

# 用于面向用户输出的高质量LLM模型，如RAG回复等重要任务
quality_llm = "openai/volcengine/deepseek-v3"

# 用于处理视觉输入的视觉语言模型（VLM）
vlm = "volcengine/doubao-seed-1.6"

# 用于音频转录的语言模型
audio_lm = "openai/whisper-1"

# 用于研究代理的推理模型
reasoning_llm = "openai/volcengine/deepseek-v3"

# 用于研究代理的规划模型
planning_llm = "openai/volcengine/deepseek-v3"



[agent]
# 代理配置 - 定义不同类型的AI代理及其可用工具

# RAG代理的静态提示模板名称
rag_agent_static_prompt = "static_rag_agent"

# RAG代理的动态提示模板名称
rag_agent_dynamic_prompt = "dynamic_rag_agent"

# RAG代理可用的工具列表
# search_file_descriptions: 搜索文件描述
# search_file_knowledge: 搜索文件知识内容
# get_file_content: 获取文件内容
# 可选添加: "web_search"（网络搜索）| "web_scrape"（网页抓取）
rag_tools = ["search_file_descriptions", "search_file_knowledge", "get_file_content"]

# 研究代理可用的工具列表
# rag: RAG检索功能
# reasoning: 推理功能
# critique: 批评/评估功能
# python_executor: Python代码执行器
research_tools = ["rag", "reasoning", "critique", "python_executor"]

[auth]
# 身份验证配置

# 身份验证提供者，使用R2R内置认证系统
provider = "r2r"

# 访问令牌的生命周期（分钟）- 60000分钟约等于41.7天
access_token_lifetime_in_minutes = 60000

# 刷新令牌的生命周期（天）
refresh_token_lifetime_in_days = 7

# 是否需要身份验证（设为false表示不需要登录即可使用）
require_authentication = false

# 是否需要邮箱验证
require_email_verification = false

# 默认管理员邮箱
default_admin_email = "<EMAIL>"

# 默认管理员密码（生产环境中应立即更改）
default_admin_password = "change_me_immediately"

[completion]
# 文本生成完成配置

# 完成服务提供者，使用R2R内置服务
provider = "r2r"

# 并发请求限制数量
concurrent_request_limit = 64

# 请求超时时间（秒）
request_timeout = 60

  [completion.generation_config]
  # 文本生成参数配置

  # 温度参数：控制生成文本的随机性，0.1表示较低随机性，生成更确定的文本
  temperature = 0.1

  # Top-p采样：核采样参数，1表示考虑所有可能的词汇
  top_p = 1

  # 最大生成令牌数量
  max_tokens_to_sample = 4_096

  # 是否启用流式输出（false表示一次性返回完整结果）
  stream = false

  # 额外的生成参数（空对象表示使用默认设置）
  add_generation_kwargs = { }

[crypto]
# 加密配置

# 加密提供者，使用bcrypt进行密码哈希
provider = "bcrypt"



[file]
provider = "s3"
bucket_name = "cscsrag"
aws_access_key_id = "BoFTuwiBLr8HEhSMIIGY"
aws_secret_access_key = "OWYMyMTlWUnA6CR21mJYxnL6E7GahuiEErJQSS92"
region_name = "us-east-1"
endpoint_url = "http://************:9000"



[database]
# 数据库配置

# 默认集合名称
default_collection_name = "Default"

# 默认集合描述
default_collection_description = "Your default collection."

# 集合摘要生成的提示模板名称
collection_summary_prompt = "collection_summary"

  [database.graph_creation_settings]
  # 知识图谱创建设置

    # 图实体描述生成的提示模板名称
    graph_entity_description_prompt = "graph_entity_description"

    # 图提取的提示模板名称
    graph_extraction_prompt = "graph_extraction"

    # 要提取的实体类型列表（空列表表示提取所有实体类型）
    entity_types = []

    # 要提取的关系类型列表（空列表表示提取所有关系类型）
    relation_types = []

    # 启用实体的自动去重功能
    automatic_deduplication = true

  [database.graph_enrichment_settings]
  # 知识图谱增强设置

    # 图社区发现的提示模板名称
    graph_communities_prompt = "graph_communities"

  [database.maintenance]
  # 数据库维护设置

    # 数据库清理计划，使用Cron表达式：每天凌晨3点执行
    vacuum_schedule = "0 3 * * *"

[embedding]
# 文本嵌入配置

# 嵌入服务提供者，使用扩展的OpenAI提供者支持自定义端点
provider = "openai"

# 阿里云text-embedding-v4模型
base_model = "aliyun/text-embedding-v4"

# 阿里云text-embedding-v4模型的向量维度
base_dimension = 1024

# 自定义OpenAI兼容API端点
base_url = "http://172.17.9.46:3000/v1"

# API密钥（也可以通过环境变量OPENAI_API_KEY设置）
api_key = "sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI"

# 重排序模型（可选，用于提高检索精度）
# rerank_model = "huggingface/mixedbread-ai/mxbai-rerank-large-v1"

# 批处理大小：每次处理的文本数量
batch_size = 50

# 并发请求限制数量
concurrent_request_limit = 50

# 初始退避时间（秒）：请求失败时的重试间隔
initial_backoff = 1.0

# 量化设置：FP32表示使用32位浮点数精度
quantization_settings = { quantization_type = "FP32" }

[completion_embedding]
# 完成任务的嵌入配置
# 通常应与主嵌入配置相同，但高级用户可能希望使用不同的提供者来减少延迟

# 嵌入服务提供者，使用扩展的OpenAI提供者
provider = "openai"

# 阿里云text-embedding-v4模型
base_model = "aliyun/text-embedding-v4"

# 阿里云text-embedding-v4模型的向量维度
base_dimension = 1024

# 自定义OpenAI兼容API端点
base_url = "http://172.17.9.46:3000/v1"

# API密钥（也可以通过环境变量OPENAI_API_KEY设置）
api_key = "sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI"

# 批处理大小：每次处理的文本数量
batch_size = 50

# 并发请求限制数量
concurrent_request_limit = 50

[ingestion]
# 文档摄取配置 - 控制如何处理和分割输入文档

# 摄取服务提供者，使用R2R内置服务
provider = "r2r"

# 文档分块策略：递归分块（根据文档结构智能分割）
chunking_strategy = "recursive"

# 每个文档块的大小（字符数）
chunk_size = 1_024

# 相邻块之间的重叠字符数（用于保持上下文连续性）
chunk_overlap = 512

# 排除的解析器列表（空列表表示使用所有可用解析器）
excluded_parsers = []

# 启用实体和关系的自动提取功能
automatic_extraction = true

# 视觉语言模型的批处理大小
vlm_batch_size = 20

# 最大并发VLM任务数量
max_concurrent_vlm_tasks = 20

# VLM OCR设置：每页生成一个文档块
vlm_ocr_one_page_per_chunk = true

  [ingestion.chunk_enrichment_settings]
  # 文档块增强设置

    # 块增强的提示模板名称
    chunk_enrichment_prompt = "chunk_enrichment"

    # 是否启用块增强功能（默认禁用）
    enable_chunk_enrichment = false

    # 用于增强的块数量（前后各n个块）
    n_chunks = 2

  [ingestion.extra_parsers]
  # 额外的文档解析器配置

    # PDF文件使用的解析器：ZeroX和OCR
    pdf = ["zerox", "ocr"]

[ocr]
# 光学字符识别（OCR）配置

# OCR服务提供者
provider = "mistral"

# OCR模型
model = "mistral-ocr-latest"

[orchestration]
# 任务编排配置

# 编排服务提供者，使用简单编排器
provider = "simple"

[email]
# 邮件服务配置

# 邮件服务提供者：console_mock（控制台模拟，用于开发测试）
# 支持的提供者：smtp、sendgrid、mailersend
provider = "console_mock"

[scheduler]
# 任务调度器配置

# 调度器提供者，使用APScheduler（高级Python调度器）
provider = "apscheduler"
