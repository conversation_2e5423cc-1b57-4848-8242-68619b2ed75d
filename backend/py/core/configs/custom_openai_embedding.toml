# Example configuration for custom OpenAI-compatible embedding endpoint
# This demonstrates how to use the extended OpenAI provider with custom endpoints

[app]
# LLM used for internal operations, like deriving conversation names
fast_llm = "openai/gpt-4o-mini"

# LLM used for user-facing output, like RAG replies
quality_llm = "openai/gpt-4o"

# LLM used for ingesting visual inputs
vlm = "openai/gpt-4o"

# LLM used for transcription
audio_lm = "openai/whisper-1"

[embedding]
provider = "openai"
# Custom model name - can be any model supported by the endpoint
base_model = "aliyun/text-embedding-v4"
# Custom dimension for the model
base_dimension = 1024
# Custom base URL for OpenAI-compatible endpoint
base_url = "http://***********:3000/v1"
# Custom API key (can also be set via environment variable)
api_key = "sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI"
batch_size = 128
concurrent_request_limit = 256

[completion_embedding]
# Generally this should be the same as the embedding config
provider = "openai"
base_model = "aliyun/text-embedding-v4"
base_dimension = 1024
base_url = "http://***********:3000/v1"
api_key = "sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI"
batch_size = 128
concurrent_request_limit = 256

[agent]
tools = ["search_file_knowledge"]

[completion]
provider = "litellm"
concurrent_request_limit = 1

  [completion.generation_config]
  temperature = 0.1
  top_p = 1
  max_tokens_to_sample = 1_024
  stream = false

[ingestion]
provider = "unstructured_local"
strategy = "auto"
chunking_strategy = "by_title"
new_after_n_chars = 512
max_characters = 1_024
combine_under_n_chars = 128
overlap = 20

[orchestration]
provider = "simple"

[auth]
provider = "r2r"
require_authentication = false
require_email_verification = false
default_admin_email = "<EMAIL>"
default_admin_password = "change_me_immediately"
access_token_lifetime_in_minutes = 60
refresh_token_lifetime_in_days = 7
secret_key = "your-secret-key"

[database]
provider = "postgres"

[crypto]
provider = "bcrypt"

[email]
provider = "console"

[file]
provider = "postgres"

[kg]
provider = "postgres"
batch_size = 1
text_splitter = "recursive_character"
text_splitter_chunk_size = 1_024
text_splitter_chunk_overlap = 256

[logging]
provider = "local"
log_table = "logs"
log_info_table = "log_info"
