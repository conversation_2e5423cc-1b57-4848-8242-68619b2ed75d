import contextlib
import logging
import os
from typing import Any

import tiktoken
from openai import Async<PERSON>penAI, AuthenticationError, OpenAI
from openai._types import NOT_GIVEN

from core.base import (
    ChunkSearchResult,
    EmbeddingConfig,
    EmbeddingProvider,
)

from .utils import truncate_texts_to_token_limit

logger = logging.getLogger()


class OpenAIEmbeddingProvider(EmbeddingProvider):
    # Default OpenAI models and their configurations
    DEFAULT_MODEL_TO_TOKENIZER = {
        "text-embedding-ada-002": "cl100k_base",
        "text-embedding-3-small": "cl100k_base",
        "text-embedding-3-large": "cl100k_base",
    }
    DEFAULT_MODEL_TO_DIMENSIONS = {
        "text-embedding-ada-002": [1536],
        "text-embedding-3-small": [512, 1536],
        "text-embedding-3-large": [256, 1024, 3072],
    }

    def __init__(self, config: EmbeddingConfig):
        super().__init__(config)
        if not config.provider:
            raise ValueError(
                "Must set provider in order to initialize OpenAIEmbeddingProvider."
            )

        if config.provider != "openai":
            raise ValueError(
                "OpenAIEmbeddingProvider must be initialized with provider `openai`."
            )

        # Determine API key - prioritize config.api_key, then environment variable
        api_key = config.api_key or os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError(
                "Must set api_key in config or OPENAI_API_KEY environment variable."
            )

        # Determine base URL - use config.base_url if provided
        base_url = config.base_url

        # Initialize OpenAI clients with custom configuration
        if base_url:
            self.client = OpenAI(api_key=api_key, base_url=base_url)
            self.async_client = AsyncOpenAI(api_key=api_key, base_url=base_url)
            logger.info(f"Using custom OpenAI-compatible endpoint: {base_url}")
        else:
            self.client = OpenAI(api_key=api_key)
            self.async_client = AsyncOpenAI(api_key=api_key)
            logger.info("Using official OpenAI endpoint")

        if config.rerank_model:
            raise ValueError(
                "OpenAIEmbeddingProvider does not support separate reranking."
            )

        # Process model name
        if config.base_model and "openai/" in config.base_model:
            self.base_model = config.base_model.split("/")[-1]
        else:
            self.base_model = config.base_model
        self.base_dimension = config.base_dimension

        if not self.base_model:
            raise ValueError(
                "Must set base_model in order to initialize OpenAIEmbeddingProvider."
            )

        # Check if this is a known OpenAI model or a custom model
        self.is_custom_model = self.base_model not in self.DEFAULT_MODEL_TO_TOKENIZER

        if not self.is_custom_model:
            # For known OpenAI models, validate dimensions
            if self.base_dimension:
                if (
                    self.base_dimension
                    not in self.DEFAULT_MODEL_TO_DIMENSIONS[self.base_model]
                ):
                    raise ValueError(
                        f"Dimensions {self.base_dimension} for {self.base_model} are not supported"
                    )
            else:
                # If base_dimension is not set, use the largest available dimension for the model
                self.base_dimension = max(
                    self.DEFAULT_MODEL_TO_DIMENSIONS[self.base_model]
                )
        else:
            # For custom models, use provided dimension or default
            if not self.base_dimension:
                logger.warning(
                    f"Custom model {self.base_model} detected. Using default dimension 1536. "
                    "Please specify base_dimension in config for optimal performance."
                )
                self.base_dimension = 1536

    def _get_dimensions(self):
        # For custom models, always return the configured dimension
        if self.is_custom_model:
            return self.base_dimension

        # For known OpenAI models, use original logic
        return (
            NOT_GIVEN
            if self.base_model == "text-embedding-ada-002"
            else self.base_dimension
            or self.DEFAULT_MODEL_TO_DIMENSIONS[self.base_model][-1]
        )

    def _get_embedding_kwargs(self, **kwargs):
        return {
            "model": self.base_model,
            "dimensions": self._get_dimensions(),
        } | kwargs

    async def _execute_task(self, task: dict[str, Any]) -> list[list[float]]:
        texts = task["texts"]
        kwargs = self._get_embedding_kwargs(**task.get("kwargs", {}))

        try:
            # Truncate text if it exceeds the model's max input tokens. Some providers do this by default, others do not.
            if kwargs.get("model"):
                with contextlib.suppress(Exception):
                    texts = truncate_texts_to_token_limit(
                        texts, kwargs["model"]
                    )

            response = await self.async_client.embeddings.create(
                input=texts,
                **kwargs,
            )
            return [data.embedding for data in response.data]
        except AuthenticationError as e:
            raise ValueError(
                "Invalid OpenAI API key provided. Please check your OPENAI_API_KEY environment variable."
            ) from e
        except Exception as e:
            error_msg = f"Error getting embeddings: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg) from e

    def _execute_task_sync(self, task: dict[str, Any]) -> list[list[float]]:
        texts = task["texts"]
        kwargs = self._get_embedding_kwargs(**task.get("kwargs", {}))
        try:
            # Truncate text if it exceeds the model's max input tokens. Some providers do this by default, others do not.
            if kwargs.get("model"):
                with contextlib.suppress(Exception):
                    texts = truncate_texts_to_token_limit(
                        texts, kwargs["model"]
                    )

            response = self.client.embeddings.create(
                input=texts,
                **kwargs,
            )
            return [data.embedding for data in response.data]
        except AuthenticationError as e:
            raise ValueError(
                "Invalid OpenAI API key provided. Please check your OPENAI_API_KEY environment variable."
            ) from e
        except Exception as e:
            error_msg = f"Error getting embeddings: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg) from e

    async def async_get_embedding(
        self,
        text: str,
        stage: EmbeddingProvider.Step = EmbeddingProvider.Step.BASE,
        **kwargs,
    ) -> list[float]:
        if stage != EmbeddingProvider.Step.BASE:
            raise ValueError(
                "OpenAIEmbeddingProvider only supports search stage."
            )

        task = {
            "texts": [text],
            "stage": stage,
            "kwargs": kwargs,
        }
        result = await self._execute_with_backoff_async(task)
        return result[0]

    def get_embedding(
        self,
        text: str,
        stage: EmbeddingProvider.Step = EmbeddingProvider.Step.BASE,
        **kwargs,
    ) -> list[float]:
        if stage != EmbeddingProvider.Step.BASE:
            raise ValueError(
                "OpenAIEmbeddingProvider only supports search stage."
            )

        task = {
            "texts": [text],
            "stage": stage,
            "kwargs": kwargs,
        }
        result = self._execute_with_backoff_sync(task)
        return result[0]

    async def async_get_embeddings(
        self,
        texts: list[str],
        stage: EmbeddingProvider.Step = EmbeddingProvider.Step.BASE,
        **kwargs,
    ) -> list[list[float]]:
        if stage != EmbeddingProvider.Step.BASE:
            raise ValueError(
                "OpenAIEmbeddingProvider only supports search stage."
            )

        task = {
            "texts": texts,
            "stage": stage,
            "kwargs": kwargs,
        }
        return await self._execute_with_backoff_async(task)

    def get_embeddings(
        self,
        texts: list[str],
        stage: EmbeddingProvider.Step = EmbeddingProvider.Step.BASE,
        **kwargs,
    ) -> list[list[float]]:
        if stage != EmbeddingProvider.Step.BASE:
            raise ValueError(
                "OpenAIEmbeddingProvider only supports search stage."
            )

        task = {
            "texts": texts,
            "stage": stage,
            "kwargs": kwargs,
        }
        return self._execute_with_backoff_sync(task)

    def rerank(
        self,
        query: str,
        results: list[ChunkSearchResult],
        stage: EmbeddingProvider.Step = EmbeddingProvider.Step.RERANK,
        limit: int = 10,
    ):
        return results[:limit]

    async def arerank(
        self,
        query: str,
        results: list[ChunkSearchResult],
        stage: EmbeddingProvider.Step = EmbeddingProvider.Step.RERANK,
        limit: int = 10,
    ):
        return results[:limit]

    def tokenize_string(self, text: str, model: str) -> list[int]:
        # For custom models, use cl100k_base as default tokenizer
        if model not in self.DEFAULT_MODEL_TO_TOKENIZER:
            logger.warning(
                f"Custom model {model} detected. Using cl100k_base tokenizer as default."
            )
            encoding = tiktoken.get_encoding("cl100k_base")
        else:
            encoding = tiktoken.get_encoding(
                self.DEFAULT_MODEL_TO_TOKENIZER[model]
            )
        return encoding.encode(text)
