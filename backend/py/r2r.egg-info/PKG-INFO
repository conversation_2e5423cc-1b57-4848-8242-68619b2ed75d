Metadata-Version: 2.4
Name: r2r
Version: 3.6.5
Summary: SciPhi R2R
Author-email: <PERSON> <<EMAIL>>
License: MIT
Requires-Python: <3.13,>=3.10
Description-Content-Type: text/markdown
Requires-Dist: aiofiles<25.0.0,>=24.1.0
Requires-Dist: alembic<2.0.0,>=1.13.3
Requires-Dist: fastapi<0.116.0,>=0.115.11
Requires-Dist: httpx>=0.27.0
Requires-Dist: openai>=1.61.0
Requires-Dist: python-dotenv<2.0.0,>=1.0.1
Requires-Dist: psycopg-binary<4.0.0,>=3.2.3
Requires-Dist: requests<3.0.0,>=2.31.0
Requires-Dist: tiktoken<0.9.0,>=0.8.0
Requires-Dist: toml<0.11.0,>=0.10.2
Requires-Dist: types-requests<3.0.0,>=2.31.0
Requires-Dist: types-aiofiles<25.0.0,>=24.1.0.20240626
Requires-Dist: typing-extensions<5.0.0,>=4.12.2
Requires-Dist: pydantic>=2.10.6
Requires-Dist: python-json-logger>=3.2.1
Requires-Dist: filetype>=1.2.0
Provides-Extra: core
Requires-Dist: aiohttp<4.0.0,>=3.10.10; extra == "core"
Requires-Dist: aioshutil<2.0,>=1.5; extra == "core"
Requires-Dist: aiosqlite<0.21.0,>=0.20.0; extra == "core"
Requires-Dist: anthropic>=0.49.0; extra == "core"
Requires-Dist: apscheduler<4.0.0,>=3.10.4; extra == "core"
Requires-Dist: asyncpg<0.30.0,>=0.29.0; extra == "core"
Requires-Dist: azure-ai-inference<2.0.0,>=1.0.0b8; extra == "core"
Requires-Dist: azure-ai-ml<2.0.0,>=1.24.0; extra == "core"
Requires-Dist: bcrypt<5.0.0,>=4.1.3; extra == "core"
Requires-Dist: beautifulsoup4<5.0.0,>=4.12.3; extra == "core"
Requires-Dist: boto3<2.0.0,>=1.35.17; extra == "core"
Requires-Dist: colorlog<7.0.0,>=6.9.0; extra == "core"
Requires-Dist: docutils<0.22.0,>=0.21.2; extra == "core"
Requires-Dist: epub<0.6.0,>=0.5.2; extra == "core"
Requires-Dist: firecrawl-py>=1.13.5; extra == "core"
Requires-Dist: fsspec<2025.0.0,>=2024.6.0; extra == "core"
Requires-Dist: future<2.0.0,>=1.0.0; extra == "core"
Requires-Dist: google-auth<3.0.0,>=2.37.0; extra == "core"
Requires-Dist: google-auth-oauthlib<2.0.0,>=1.2.1; extra == "core"
Requires-Dist: google-genai<0.7.0,>=0.6.0; extra == "core"
Requires-Dist: gunicorn<22.0.0,>=21.2.0; extra == "core"
Requires-Dist: hatchet-sdk==0.47.0; extra == "core"
Requires-Dist: litellm>=1.69.3; extra == "core"
Requires-Dist: markdown<4.0,>=3.6; extra == "core"
Requires-Dist: mistralai>=1.5.2; extra == "core"
Requires-Dist: msg-parser>=1.2.0; extra == "core"
Requires-Dist: networkx<4.0,>=3.3; extra == "core"
Requires-Dist: numpy<1.29.0,>=1.22.4; extra == "core"
Requires-Dist: olefile<0.48,>=0.47; extra == "core"
Requires-Dist: ollama<0.4.0,>=0.3.1; extra == "core"
Requires-Dist: openpyxl<4.0.0,>=3.1.2; extra == "core"
Requires-Dist: orgparse<0.5.0,>=0.4.20231004; extra == "core"
Requires-Dist: pdf2image>=1.17.0; extra == "core"
Requires-Dist: pillow<12.0.0,>=11.1.0; extra == "core"
Requires-Dist: pillow-heif<0.22.0,>=0.21.0; extra == "core"
Requires-Dist: psutil<7.0.0,>=6.0.0; extra == "core"
Requires-Dist: pydantic[email]<3.0.0,>=2.8.2; extra == "core"
Requires-Dist: pyjwt<3.0.0,>=2.8.0; extra == "core"
Requires-Dist: pynacl<2.0.0,>=1.5.0; extra == "core"
Requires-Dist: pypdf<5.0.0,>=4.2.0; extra == "core"
Requires-Dist: pypdf2<4.0.0,>=3.0.1; extra == "core"
Requires-Dist: python-docx<2.0.0,>=1.1.0; extra == "core"
Requires-Dist: python-multipart<0.0.19,>=0.0.9; extra == "core"
Requires-Dist: python-pptx<2.0.0,>=1.0.1; extra == "core"
Requires-Dist: pyyaml<7.0.0,>=6.0.1; extra == "core"
Requires-Dist: sendgrid<7.0.0,>=6.11.0; extra == "core"
Requires-Dist: mailersend<0.6.0,>=0.5.6; extra == "core"
Requires-Dist: sentry-sdk<3.0.0,>=2.20.0; extra == "core"
Requires-Dist: sqlalchemy<3.0.0,>=2.0.30; extra == "core"
Requires-Dist: striprtf<0.0.29,>=0.0.28; extra == "core"
Requires-Dist: supabase<3.0.0,>=2.15.0; extra == "core"
Requires-Dist: tokenizers==0.19; extra == "core"
Requires-Dist: unstructured-client==0.34.0; extra == "core"
Requires-Dist: uvicorn<0.28.0,>=0.27.0.post1; extra == "core"
Requires-Dist: vecs<0.5.0,>=0.4.0; extra == "core"
Requires-Dist: xlrd<3.0.0,>=2.0.1; extra == "core"

<img width="1217" alt="Screenshot 2025-03-27 at 6 35 02 AM" src="https://github.com/user-attachments/assets/10b530a6-527f-4335-b2e4-ceaa9fc1219f" />

<h3 align="center">
The most advanced AI retrieval system.

Agentic Retrieval-Augmented Generation (RAG) with a RESTful API.
</h3>

<div align="center">
   <div>
      <a href="https://r2r-docs.sciphi.ai/"><strong>Docs</strong></a> ·
      <a href="https://github.com/SciPhi-AI/R2R/issues/new?assignees=&labels=&projects=&template=bug_report.md&title="><strong>Report Bug</strong></a> ·
      <a href="https://github.com/SciPhi-AI/R2R/issues/new?assignees=&labels=&projects=&template=feature_request.md&title="><strong>Feature Request</strong></a> ·
      <a href="https://discord.gg/p6KqD2kjtB"><strong>Discord</strong></a>
   </div>
   <br />
   <p align="center">
    <a href="https://r2r-docs.sciphi.ai"><img src="https://img.shields.io/badge/docs.sciphi.ai-3F16E4" alt="Docs"></a>
    <a href="https://discord.gg/p6KqD2kjtB"><img src="https://img.shields.io/discord/1120774652915105934?style=social&logo=discord" alt="Discord"></a>
    <a href="https://github.com/SciPhi-AI"><img src="https://img.shields.io/github/stars/SciPhi-AI/R2R" alt="Github Stars"></a>
    <a href="https://github.com/SciPhi-AI/R2R/pulse"><img src="https://img.shields.io/github/commit-activity/w/SciPhi-AI/R2R" alt="Commits-per-week"></a>
    <a href="https://opensource.org/licenses/MIT"><img src="https://img.shields.io/badge/License-MIT-purple.svg" alt="License: MIT"></a>
  </p>
</div>

# About
R2R is an advanced AI retrieval system supporting Retrieval-Augmented Generation (RAG) with production-ready features. Built around a RESTful API, R2R offers multimodal content ingestion, hybrid search, knowledge graphs, and comprehensive document management.

R2R also includes a **Deep Research API**, a multi-step reasoning system that fetches relevant data from your knowledgebase and/or the internet to deliver richer, context-aware answers for complex queries.

# Usage

```python
# Basic search
results = client.retrieval.search(query="What is DeepSeek R1?")

# RAG with citations
response = client.retrieval.rag(query="What is DeepSeek R1?")

# Deep Research RAG Agent
response = client.retrieval.agent(
  message={"role":"user", "content": "What does deepseek r1 imply? Think about market, societal implications, and more."},
  rag_generation_config={
    "model"="anthropic/claude-3-7-sonnet-20250219",
    "extended_thinking": True,
    "thinking_budget": 4096,
    "temperature": 1,
    "top_p": None,
    "max_tokens_to_sample": 16000,
  },
)
```



## Getting Started
```bash
# Quick install and run in light mode
pip install r2r
export OPENAI_API_KEY=sk-...
python -m r2r.serve

# Or run in full mode with Docker
# <NAME_EMAIL>:SciPhi-AI/R2R.git && cd R2R
# export R2R_CONFIG_NAME=full OPENAI_API_KEY=sk-...
# docker compose -f compose.full.yaml --profile postgres up -d
```

For detailed self-hosting instructions, see the [self-hosting docs](https://r2r-docs.sciphi.ai/self-hosting/installation/overview).

## Demo
https://github.com/user-attachments/assets/173f7a1f-7c0b-4055-b667-e2cdcf70128b

## Using the API

### 1. Install SDK & Setup

```bash
# Install SDK
pip install r2r  # Python
# or
npm i r2r-js    # JavaScript
```

### 2. Client Initialization

```python
from r2r import R2RClient
client = R2RClient(base_url="http://localhost:7272")
```

```javascript
const { r2rClient } = require('r2r-js');
const client = new r2rClient("http://localhost:7272");
```

### 3. Document Operations

```python
# Ingest sample or your own document
client.documents.create(file_path="/path/to/file")

# List documents
client.documents.list()
```


## Key Features

- **📁 Multimodal Ingestion**: Parse `.txt`, `.pdf`, `.json`, `.png`, `.mp3`, and more
- **🔍 Hybrid Search**: Semantic + keyword search with reciprocal rank fusion
- **🔗 Knowledge Graphs**: Automatic entity & relationship extraction
- **🤖 Agentic RAG**: Reasoning agent integrated with retrieval
- **🔐 User & Access Management**: Complete authentication & collection system

## Community & Contributing

- [Join our Discord](https://discord.gg/p6KqD2kjtB) for support and discussion
- Submit [feature requests](https://github.com/SciPhi-AI/R2R/issues/new?assignees=&labels=&projects=&template=feature_request.md&title=) or [bug reports](https://github.com/SciPhi-AI/R2R/issues/new?assignees=&labels=&projects=&template=bug_report.md&title=)
- Open PRs for new features, improvements, or documentation

### Our Contributors
<a href="https://github.com/SciPhi-AI/R2R/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=SciPhi-AI/R2R" />
</a>
