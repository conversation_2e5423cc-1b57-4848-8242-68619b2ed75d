# 自定义OpenAI兼容Embedding配置指南

本指南详细说明如何配置系统以使用自定义的OpenAI兼容embedding接口，特别是阿里云text-embedding-v4模型。

## 配置概览

系统现在支持通过扩展的OpenAI provider接入任何OpenAI兼容的embedding API，包括：
- 阿里云text-embedding-v4
- 其他第三方OpenAI兼容服务
- 自建的OpenAI兼容API

## 1. 环境变量配置 (backend/.env)

```bash
# OpenAI API配置 - 用于自定义OpenAI兼容端点
export OPENAI_API_KEY=sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI
export OPENAI_BASE_URL=http://***********:3000/v1

# PostgreSQL数据库配置
export R2R_POSTGRES_USER=postgres
export R2R_POSTGRES_PASSWORD=postgres
export R2R_POSTGRES_HOST=************
export R2R_POSTGRES_PORT=5432
export R2R_POSTGRES_DBNAME=postgres
export R2R_PROJECT_NAME=cscs_rag
```

### 环境变量说明

| 变量名 | 必需 | 说明 |
|--------|------|------|
| `OPENAI_API_KEY` | ✅ | 自定义API的密钥 |
| `OPENAI_BASE_URL` | ❌ | 自定义API端点URL（可在配置文件中设置） |
| `R2R_POSTGRES_*` | ✅ | PostgreSQL数据库连接信息 |
| `R2R_PROJECT_NAME` | ✅ | 项目名称 |

## 2. 主配置文件 (backend/py/r2r/r2r.toml)

### Embedding配置

```toml
[embedding]
# 使用扩展的OpenAI提供者
provider = "openai"

# 阿里云text-embedding-v4模型
base_model = "aliyun/text-embedding-v4"

# 模型向量维度
base_dimension = 1024

# 自定义API端点
base_url = "http://***********:3000/v1"

# API密钥（可选，优先级高于环境变量）
api_key = "sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI"

# 性能配置
batch_size = 50
concurrent_request_limit = 50
initial_backoff = 1.0
quantization_settings = { quantization_type = "FP32" }
```

### Completion Embedding配置

```toml
[completion_embedding]
# 通常与主embedding配置相同
provider = "openai"
base_model = "aliyun/text-embedding-v4"
base_dimension = 1024
base_url = "http://***********:3000/v1"
api_key = "sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI"
batch_size = 50
concurrent_request_limit = 50
```

## 3. 配置参数详解

### 核心参数

- **provider**: 必须设置为 `"openai"` 以使用扩展的OpenAI提供者
- **base_model**: 模型名称，支持任意格式（如 `aliyun/text-embedding-v4`）
- **base_dimension**: embedding向量维度，必须与模型实际输出维度匹配
- **base_url**: 自定义API端点URL
- **api_key**: API密钥，可在配置文件中设置或使用环境变量

### 性能参数

- **batch_size**: 批处理大小，建议50-100
- **concurrent_request_limit**: 并发请求限制，根据API限制调整
- **initial_backoff**: 重试间隔，建议1.0秒

## 4. 配置验证

运行验证脚本检查配置是否正确：

```bash
python validate_config.py
```

验证脚本会检查：
- ✅ 环境变量是否正确设置
- ✅ 配置文件格式是否正确
- ✅ Embedding提供者是否能正常初始化
- ✅ Tokenization功能是否正常

## 5. 配置优先级

系统按以下优先级读取配置：

1. **配置文件中的api_key** > 环境变量OPENAI_API_KEY
2. **配置文件中的base_url** > 环境变量OPENAI_BASE_URL
3. **配置文件中的其他参数** > 默认值

## 6. 常见配置场景

### 场景1：阿里云text-embedding-v4（当前配置）

```toml
[embedding]
provider = "openai"
base_model = "aliyun/text-embedding-v4"
base_dimension = 1024
base_url = "http://***********:3000/v1"
api_key = "sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI"
```

### 场景2：其他自定义模型

```toml
[embedding]
provider = "openai"
base_model = "custom/my-embedding-model"
base_dimension = 768
base_url = "https://api.example.com/v1"
api_key = "your-api-key"
```

### 场景3：标准OpenAI（向后兼容）

```toml
[embedding]
provider = "openai"
base_model = "text-embedding-3-small"
base_dimension = 512
# 不设置base_url，使用官方OpenAI API
```

## 7. 故障排除

### 常见问题

1. **连接失败**
   - 检查base_url是否正确
   - 确认网络连接和防火墙设置
   - 验证端点是否可访问

2. **认证失败**
   - 验证api_key是否正确
   - 检查API密钥权限
   - 确认密钥格式正确

3. **维度错误**
   - 确认base_dimension与模型实际输出一致
   - 查看API文档获取正确维度

4. **性能问题**
   - 调整batch_size和concurrent_request_limit
   - 监控API响应时间和限制

### 调试步骤

1. 运行配置验证脚本
2. 检查系统日志
3. 测试API端点连通性
4. 验证API密钥有效性

## 8. 安全建议

1. **不要在配置文件中硬编码API密钥**
   - 优先使用环境变量
   - 使用密钥管理服务

2. **网络安全**
   - 使用HTTPS端点
   - 配置适当的防火墙规则

3. **访问控制**
   - 限制API密钥权限
   - 定期轮换密钥

## 9. 监控和维护

1. **性能监控**
   - 监控embedding生成延迟
   - 跟踪API调用成功率
   - 监控资源使用情况

2. **定期检查**
   - 验证API端点可用性
   - 检查配置文件完整性
   - 更新API密钥

## 10. 升级和迁移

当需要更换embedding服务时：

1. 更新配置文件中的相关参数
2. 运行验证脚本确认配置正确
3. 测试新配置的功能
4. 逐步迁移生产环境

通过以上配置，您的系统将能够成功接入自定义的OpenAI兼容embedding服务。
