#!/usr/bin/env python3
"""
配置验证脚本
验证自定义OpenAI兼容embedding配置是否正确
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent / "backend" / "py"
sys.path.insert(0, str(backend_dir))

def load_env_file():
    """加载.env文件中的环境变量"""
    env_file = Path(__file__).parent / "backend" / ".env"
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    # 移除export前缀
                    if line.startswith('export '):
                        line = line[7:]
                    key, value = line.split('=', 1)
                    os.environ[key] = value
        print("✓ 已加载.env文件中的环境变量")
    else:
        print("⚠ 未找到.env文件")

def validate_environment():
    """验证环境变量配置"""
    print("\n=== 环境变量验证 ===")
    
    required_vars = {
        'OPENAI_API_KEY': '自定义API密钥',
        'R2R_POSTGRES_USER': 'PostgreSQL用户名',
        'R2R_POSTGRES_PASSWORD': 'PostgreSQL密码',
        'R2R_POSTGRES_HOST': 'PostgreSQL主机',
        'R2R_POSTGRES_PORT': 'PostgreSQL端口',
        'R2R_POSTGRES_DBNAME': 'PostgreSQL数据库名',
        'R2R_PROJECT_NAME': '项目名称'
    }
    
    optional_vars = {
        'OPENAI_BASE_URL': '自定义API端点URL'
    }
    
    all_good = True
    
    # 检查必需的环境变量
    for var, desc in required_vars.items():
        value = os.getenv(var)
        if value:
            # 对于敏感信息，只显示前几个字符
            if 'KEY' in var or 'PASSWORD' in var:
                display_value = f"{value[:8]}..." if len(value) > 8 else value
            else:
                display_value = value
            print(f"✓ {var}: {display_value} ({desc})")
        else:
            print(f"❌ {var}: 未设置 ({desc})")
            all_good = False
    
    # 检查可选的环境变量
    for var, desc in optional_vars.items():
        value = os.getenv(var)
        if value:
            print(f"✓ {var}: {value} ({desc})")
        else:
            print(f"ℹ {var}: 未设置 ({desc}) - 可选")
    
    return all_good

def validate_toml_config():
    """验证r2r.toml配置文件"""
    print("\n=== r2r.toml配置验证 ===")
    
    try:
        import toml
    except ImportError:
        print("❌ 需要安装toml库: pip install toml")
        return False
    
    toml_file = Path(__file__).parent / "backend" / "py" / "r2r" / "r2r.toml"
    
    if not toml_file.exists():
        print(f"❌ 未找到配置文件: {toml_file}")
        return False
    
    try:
        with open(toml_file, 'r', encoding='utf-8') as f:
            config = toml.load(f)
        
        print(f"✓ 成功加载配置文件: {toml_file}")
        
        # 验证embedding配置
        embedding_config = config.get('embedding', {})
        
        required_fields = {
            'provider': 'openai',
            'base_model': 'aliyun/text-embedding-v4',
            'base_dimension': 1024,
            'base_url': 'http://***********:3000/v1'
        }
        
        all_good = True
        
        for field, expected in required_fields.items():
            actual = embedding_config.get(field)
            if actual == expected:
                print(f"✓ embedding.{field}: {actual}")
            elif actual is not None:
                print(f"⚠ embedding.{field}: {actual} (期望: {expected})")
            else:
                print(f"❌ embedding.{field}: 未设置 (期望: {expected})")
                all_good = False
        
        # 检查API密钥配置
        api_key = embedding_config.get('api_key')
        if api_key:
            display_key = f"{api_key[:8]}..." if len(api_key) > 8 else api_key
            print(f"✓ embedding.api_key: {display_key}")
        else:
            print("ℹ embedding.api_key: 未在配置文件中设置，将使用环境变量")
        
        # 验证completion_embedding配置
        completion_embedding_config = config.get('completion_embedding', {})
        if completion_embedding_config.get('provider') == 'openai':
            print("✓ completion_embedding配置使用openai提供者")
        else:
            print("⚠ completion_embedding配置未使用openai提供者")
        
        return all_good
        
    except Exception as e:
        print(f"❌ 解析配置文件失败: {e}")
        return False

async def test_embedding_provider():
    """测试embedding提供者是否能正常工作"""
    print("\n=== Embedding提供者测试 ===")
    
    try:
        from core.base import EmbeddingConfig
        from core.providers.embeddings.openai import OpenAIEmbeddingProvider
        
        # 创建配置
        config = EmbeddingConfig(
            provider="openai",
            base_model="aliyun/text-embedding-v4",
            base_dimension=1024,
            base_url="http://***********:3000/v1",
            api_key=os.getenv("OPENAI_API_KEY"),
            batch_size=50,
            concurrent_request_limit=50
        )
        
        print("✓ 配置对象创建成功")
        
        # 初始化提供者
        provider = OpenAIEmbeddingProvider(config)
        print("✓ OpenAI embedding提供者初始化成功")
        print(f"  模型: {provider.base_model}")
        print(f"  维度: {provider.base_dimension}")
        print(f"  是否为自定义模型: {provider.is_custom_model}")
        
        # 测试tokenization（不需要网络请求）
        test_text = "这是一个测试文本"
        tokens = provider.tokenize_string(test_text, provider.base_model)
        print(f"✓ Tokenization测试成功")
        print(f"  文本: {test_text}")
        print(f"  Token数量: {len(tokens)}")
        
        print("\n⚠ 注意: 实际的embedding生成需要网络连接到指定的API端点")
        print("  如需测试完整功能，请确保网络连接正常且API端点可访问")
        
        return True
        
    except Exception as e:
        print(f"❌ Embedding提供者测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("自定义OpenAI兼容Embedding配置验证")
    print("=" * 50)
    
    # 加载环境变量
    load_env_file()
    
    # 验证环境变量
    env_ok = validate_environment()
    
    # 验证配置文件
    config_ok = validate_toml_config()
    
    # 测试embedding提供者
    provider_ok = asyncio.run(test_embedding_provider())
    
    # 总结
    print("\n" + "=" * 50)
    print("验证结果总结:")
    print(f"环境变量: {'✓ 通过' if env_ok else '❌ 失败'}")
    print(f"配置文件: {'✓ 通过' if config_ok else '❌ 失败'}")
    print(f"提供者测试: {'✓ 通过' if provider_ok else '❌ 失败'}")
    
    if env_ok and config_ok and provider_ok:
        print("\n🎉 所有验证通过！系统已准备就绪。")
        return 0
    else:
        print("\n⚠ 存在配置问题，请检查上述错误信息。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
