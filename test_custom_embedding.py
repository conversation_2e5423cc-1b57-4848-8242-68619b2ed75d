#!/usr/bin/env python3
"""
Test script for custom OpenAI-compatible embedding endpoint
This script demonstrates how to use the extended OpenAI embedding provider
with custom endpoints like the aliyun/text-embedding-v4 model.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent / "backend" / "py"
sys.path.insert(0, str(backend_dir))

from core.base import EmbeddingConfig
from core.providers.embeddings.openai import OpenAIEmbeddingProvider


async def test_custom_embedding():
    """Test the custom embedding configuration"""
    
    # Configuration for custom OpenAI-compatible endpoint
    config = EmbeddingConfig(
        provider="openai",
        base_model="aliyun/text-embedding-v4",
        base_dimension=1024,
        base_url="http://***********:3000/v1",
        api_key="sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI",
        batch_size=128,
        concurrent_request_limit=256
    )
    
    try:
        # Initialize the provider
        print("Initializing OpenAI embedding provider with custom endpoint...")
        provider = OpenAIEmbeddingProvider(config)
        print(f"✓ Provider initialized successfully")
        print(f"  Model: {provider.base_model}")
        print(f"  Dimension: {provider.base_dimension}")
        print(f"  Is custom model: {provider.is_custom_model}")
        
        # Test single embedding
        print("\nTesting single embedding...")
        test_text = "This is a test sentence for embedding."
        embedding = await provider.async_get_embedding(test_text)
        print(f"✓ Single embedding successful")
        print(f"  Text: {test_text}")
        print(f"  Embedding dimension: {len(embedding)}")
        print(f"  First 5 values: {embedding[:5]}")
        
        # Test batch embeddings
        print("\nTesting batch embeddings...")
        test_texts = [
            "First test sentence.",
            "Second test sentence.",
            "Third test sentence with more content."
        ]
        embeddings = await provider.async_get_embeddings(test_texts)
        print(f"✓ Batch embeddings successful")
        print(f"  Number of texts: {len(test_texts)}")
        print(f"  Number of embeddings: {len(embeddings)}")
        print(f"  Each embedding dimension: {len(embeddings[0])}")
        
        # Test tokenization
        print("\nTesting tokenization...")
        tokens = provider.tokenize_string(test_text, provider.base_model)
        print(f"✓ Tokenization successful")
        print(f"  Text: {test_text}")
        print(f"  Number of tokens: {len(tokens)}")
        print(f"  First 10 tokens: {tokens[:10]}")
        
        print("\n🎉 All tests passed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()


def test_standard_openai():
    """Test that standard OpenAI configuration still works"""
    
    # Set a dummy API key for testing (won't actually call API)
    os.environ["OPENAI_API_KEY"] = "sk-dummy-key-for-testing"
    
    try:
        # Configuration for standard OpenAI
        config = EmbeddingConfig(
            provider="openai",
            base_model="text-embedding-3-small",
            base_dimension=512
        )
        
        print("Testing standard OpenAI configuration...")
        provider = OpenAIEmbeddingProvider(config)
        print(f"✓ Standard OpenAI provider initialized successfully")
        print(f"  Model: {provider.base_model}")
        print(f"  Dimension: {provider.base_dimension}")
        print(f"  Is custom model: {provider.is_custom_model}")
        
    except Exception as e:
        print(f"❌ Standard OpenAI test failed: {str(e)}")


if __name__ == "__main__":
    print("Testing Custom OpenAI-Compatible Embedding Provider")
    print("=" * 60)
    
    # Test standard OpenAI configuration first
    test_standard_openai()
    print()
    
    # Test custom endpoint configuration
    asyncio.run(test_custom_embedding())
